import { Page<PERSON>ontainer, ProCard, type ProColumns, ProTable, StatisticCard } from '@ant-design/pro-components';
import React, { useReducer, useRef } from 'react';
import { useIntl } from 'umi';
import type { ActionType } from '@ant-design/pro-components';
import { Button, Menu, Dropdown, message } from 'antd';
import { fetchList, editLevel } from '@/services/operation/club';
import EditModal from './components/EditModal';
import { ConfirmButton, DefaultButton } from '@/components/Button';
import {
  HistoryOutlined
} from '@ant-design/icons';
import { useIsMobile } from '@/hooks/useTool';
import '../../card.module.less'
import TimePicker from "@/components/TimePicker";
import moment, { Moment } from "moment/moment";
import Address from "@/components/Address";
import '@/pages/card.module.less'

enum ActionTypeEnum {
  CLOSE,
  OPEN,
}

interface Action {
  type: ActionTypeEnum;
  payload?: API.EditClubNameID;
}

const Club: React.FC = () => {
  const intl = useIntl();
  const isMobile = useIsMobile();
  const actionRef = useRef<ActionType>();
  const [state, dispatch] = useReducer(
    (pre: any, action: Action) => {
      console.log('ddd', action.payload?.club_id)
      switch (action.type) {
        case ActionTypeEnum.OPEN:
          return {
            id: action.payload?.club_id,
            club_name_id: action.payload?.club_name_id,
            visible: true,
            title: intl.formatMessage({ id: 'pages.operation.user.edit_name_id' }),
          };
        case ActionTypeEnum.CLOSE:
          return {
            visible: false,
            title: '',
            id: undefined,
          };
        default:
          return pre;
      }
    },
    { visible: false, title: '' },
  );
  const columns: ProColumns<API.Columns>[] = [
    {
      title: intl.formatMessage({ id: 'pages.operation.club.search' }),
      dataIndex: 'club',
      key: 'club',
      hideInTable: true,
      width: 200,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.club.id' }),
      dataIndex: 'club_id',
      key: 'club_id',
      width: 100,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.club.name' }),
      dataIndex: 'club_name',
      key: 'club_name',
      width: 100,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.user.club_name_id' }),
      dataIndex: 'club_name_id',
      key: 'club_name_id',
      width: 100,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.club.owner' }),
      dataIndex: 'owner',
      key: 'owner',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.club.owner_uid' }),
      dataIndex: 'owner_id',
      key: 'owner_id',
      width: 100,
    }, {
      title: intl.formatMessage({ id: 'pages.operation.club.owner_address' }),
      dataIndex: 'owner_addr',
      key: 'owner_addr',
      width: 100,
      render: (_, record) => {
        return <Address address={record.owner_addr}/>
      }
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.coin' }),
      dataIndex: 'coin',
      key: 'coin',
      hideInTable: true,
      valueEnum: {
        USDT: {
          text: 'USDT',
        },
        AK: {
          text: 'AK',
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.club.region' }),
      dataIndex: 'region',
      key: 'region',
      width: 100,
      valueEnum: {
        Japan: {
          text: 'Japan',
        },
        HK: {
          text: 'HK',
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.user.channel' }),
      dataIndex: 'channel',
      key: 'channel',
      width: 150,
      valueEnum: {
        Gametalk: {
          text: 'Gametalk',
        },
        App: {
          text: 'App',
        },
      },
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.user.records' }),
      dataIndex: 'records',
      key: 'records',
      search: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.user.hands' }),
      dataIndex: 'hands',
      key: 'hands',
      search: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.user.last_active' }),
      dataIndex: 'lastActive',
      key: 'lastActive',
      search: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.user.balance' }),
      dataIndex: 'balance',
      key: 'balance',
      search: false,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.club.Vault' }),
      dataIndex: 'Vault',
      key: 'Vault',
      search: false,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.club.Jackpot' }),
      dataIndex: 'Jackpot',
      key: 'Jackpot',
      search: false,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.user.deposit' }),
      dataIndex: 'deposit',
      key: 'deposit',
      search: false,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.user.withdraw' }),
      dataIndex: 'withdraw',
      key: 'withdraw',
      search: false,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.club.income' }),
      dataIndex: 'income',
      key: 'income',
      search: false,
      width: 200,
      render: (_, record) => {
        return (
          <div>
            <div>{record.income}</div>
            <div>
              · {intl.formatMessage({ id: 'pages.operation.club.income_rack' })}:
              {record.rack}
            </div>
            <div>
              · {intl.formatMessage({ id: 'pages.operation.club.income_consumption' })}:
              {record.comsumption}
            </div>
            <div>
              · {intl.formatMessage({ id: 'pages.operation.club.income_insurance' })}:
              {record.insurance}
            </div>
          </div>
        )
      }
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.club.history_income' }),
      dataIndex: 'history_income',
      key: 'history_income',
      search: false,
      width: 200,
      render: (_, record) => {
        return (
          <div>
            <div>{record.history_income}</div>
            <div>
              · {intl.formatMessage({ id: 'pages.operation.club.income_rack' })}:
              {record.history_rack}
            </div>
            <div>
              · {intl.formatMessage({ id: 'pages.operation.club.income_consumption' })}:
              {record.history_comsumption}
            </div>
            <div>
              · {intl.formatMessage({ id: 'pages.operation.club.income_insurance' })}:
              {record.history_insurance}
            </div>
          </div>
        )
      }
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.club.star_level' }),
      dataIndex: 'star_level',
      key: 'star_level',
      search: false,
      width: 120,
    },
    {
      title: intl.formatMessage({ id: 'pages.operation.records.time' }),
      dataIndex: 'time',
      key: 'time',
      width: 120,
      colSize: 3,
      hideInTable: true,
      renderFormItem: (_, { type, defaultRender, ...rest }, form) => {
        if (type === 'form') {
          return null;
        }
        return (
          <TimePicker
            defaultLeftValue={'7D'}
            onChange={(value: [Moment, Moment] | undefined) => {
              form.setFieldsValue({
                time: value ? [moment(value[0]), moment(value[1])] : undefined,
              });
            }}
          />
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'pages.operation' }),
      dataIndex: 'operation',
      key: 'operation',
      search: false,
      width: 170,
      fixed: isMobile ? false : 'right',
      render: (_, record: any) => {
        return (
          <>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button type="link" size="small">
                {intl.formatMessage({ id: 'pages.operation.user.go_record' })}
              </Button>
              <Button type="link" size="small">
                {intl.formatMessage({ id: 'pages.operation.user.go_deposit_withdraw' })}
              </Button>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <>
                <ConfirmButton
                  key="freeze_account"
                  code="freeze_account"
                  title={intl.formatMessage({ id: 'pages.operation.club.freeze_account' })}
                  tooltip={intl.formatMessage({ id: 'pages.operation.club.freeze_account.confirm' })}
                  onConfirm={async () => {
                  }}
                />
                <ConfirmButton
                  key="thaw_account"
                  code="thaw_account"
                  title={intl.formatMessage({ id: 'pages.operation.user.thaw_account' })}
                  tooltip={intl.formatMessage({ id: 'pages.operation.user.thaw_account.confirm' })}
                  onConfirm={async () => {
                  }}
                />
              </>
              <>
                <ConfirmButton
                  type="text"
                  key="freeze_account"
                  code="freeze_account"
                  title={intl.formatMessage({ id: 'pages.operation.user.freeze_wallet' })}
                  tooltip={intl.formatMessage({ id: 'pages.operation.user.freeze_wallet.confirm' })}
                  onConfirm={async () => {
                    // }
                  }}
                />
                <ConfirmButton
                  key="thaw_account"
                  code="thaw_account"
                  title={intl.formatMessage({ id: 'pages.operation.user.thaw_wallet' })}
                  tooltip={intl.formatMessage({ id: 'pages.operation.user.thaw_wallet.confirm' })}
                  onConfirm={async () => {
                  }}
                />
              </>

            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Dropdown overlay={createMenu(record.club_id)} trigger={['click']}>
                <DefaultButton
                  key="set_star"
                  code="set_star"
                  title={intl.formatMessage({ id: 'pages.operation.user.set_star' })}
                />
              </Dropdown>
              <DefaultButton
                key="name_id"
                code="name_id"
                title={intl.formatMessage({ id: 'pages.operation.user.edit_name_id' })}
                onClick={() => {
                  dispatch({ type: ActionTypeEnum.OPEN, payload: record });
                }}
              />
            </div>
          </>
        )
      }
    },
  ];

  const handleMenuClick = (e: any, id: number) => {
    console.log(`Selected value: ${e.key}`);
    // 1:permanent,2:temporary,3:cancel
    const body: any = {
      level_type: Number(e.key)
    }
    if (Number(e.key) < 3) {
      body.new_level = 3
    }
    editLevel(id, body).then(() => {
      actionRef.current?.reload();
      message.success(
        intl.formatMessage({ id: 'pages.operation.success' }),
      );
    })
  };

  const createMenu = (id: number) => (
    <Menu onClick={(e) => handleMenuClick(e, id)}>
      <Menu.Item key={1}>
        {intl.formatMessage({ id: 'pages.operation.user.set_star_forever' })}
      </Menu.Item>
      <Menu.Item key={2}>
        {intl.formatMessage({ id: 'pages.operation.user.set_star_normal' })}
      </Menu.Item>
      <Menu.Item key={3}>
        {intl.formatMessage({ id: 'pages.operation.user.set_star_cancel' })}
      </Menu.Item>
    </Menu>
  );

  const cardStyle = { height: '100px' }

  return (
    <PageContainer
      title={intl.formatMessage({ id: 'menu.operation.club' })}
      extra={
        [
          <Button
            type="link"
            icon={<HistoryOutlined/>}
            onClick={() => {

            }}
          />
        ]
      }>
      <ProTable<API.Columns, API.PaginationParam>
        columns={columns}
        key={String(isMobile)}
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        cardBordered
        search={{
          labelWidth: 'auto',
          collapsed: false,
          collapseRender: false,
          span: { xs: 24, sm: 24, md: 6, lg: 6, xl: 6, xxl: 6 },
        }}
        pagination={{ pageSize: 10, showSizeChanger: true }}
        dateFormatter="string"
        scroll={{ x: isMobile ? undefined : 1500 }}
        tableExtraRender={() => (
          <ProCard
            direction="row"
            wrap
            size="small"
            gutter={[8, 8]}
            style={{
              paddingRight: isMobile ? 0 : '40%',
              minWidth: isMobile ? 'auto' : '1200px',
              paddingInline: 0,
              paddingBlock: 0
            }}>
            <StatisticCard
              className="dashboard-card"
              style={{ ...cardStyle }}
              bordered
              colSpan={isMobile ? 12 : 4}
              statistic={{
                title: `${intl.formatMessage({ id: 'pages.operation.user.records' })}`,
                value: `${999}`,
              }}
            />
            <StatisticCard
              className="dashboard-card"
              style={{ ...cardStyle }}
              bordered
              colSpan={isMobile ? 12 : 4}
              statistic={{
                title: `${intl.formatMessage({ id: 'pages.operation.user.hands' })}`,
                value: `${999}`,
              }}
            />
            <StatisticCard
              direction="row"
              style={{ ...cardStyle, paddingInline: 0, paddingBlock: 0 }}
              statistic={{
                title: `${intl.formatMessage({ id: 'pages.operation.club.income' })}`,
                value: `${999888}`,
              }}
              bordered
              colSpan={isMobile ? 24 : 6}
              size="small">
              <ProCard ghost style={{ display: 'flex', marginLeft: '32px' }}>
                <div>
                  ·<span>{intl.formatMessage({ id: 'pages.operation.club.income_rack' })}</span>
                  ：<span>{12}</span>
                </div>
                <div>
                  ·<span>{intl.formatMessage({ id: 'pages.operation.club.income_consumption' })}</span>
                  ：<span>{12}</span>
                </div>
                <div>
                  ·<span>{intl.formatMessage({ id: 'pages.operation.club.income_insurance' })}</span>
                  ：<span>{12}</span>
                </div>
              </ProCard>
            </StatisticCard>
          </ProCard>
        )}
      />
      <EditModal
        visible={state.visible}
        title={state.title}
        id={state.id}
        club_name_id={state.club_name_id}
        onCancel={() => {
          dispatch({ type: ActionTypeEnum.CLOSE });
        }}
        onSuccess={() => {
          dispatch({ type: ActionTypeEnum.CLOSE });
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Club;
