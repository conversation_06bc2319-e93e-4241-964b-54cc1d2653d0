import { request } from 'umi';

export async function fetchList(params?: API.PaginationParam, options?: { [key: string]: any }) {
  return request<API.ResponseResult<API.ClubInfo[]>>('/api/v1/game/clubs', {
    method: 'GET',
    params: {
      current: params?.current || 1,
      pageSize: params?.pageSize || 10,
      ...params,
    },
    ...(options || {}),
  }).then(response => {
    // 处理响应数据，确保与组件期望的格式一致
    return {
      data: response.data || [],
      success: response.success,
      total: response.total || 0,
    };
  });
}

export async function editLevel(id: number, body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<any>>(`/api/v1/game/clubs/${id}/level`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

export async function editNameId(id: number, body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<any>>(`/api/v1/game/clubs/${id}/nameid`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}