export default {
  'pages.layouts.userLayout.title': 'Welcome',
  'pages.login.accountLogin.tab': 'Account Login',
  'pages.login.failure': '<PERSON>gin failed, please try again!',
  'pages.login.success': 'Login successful!',
  'pages.login.username.placeholder': 'Username',
  'pages.login.username.required': 'Please input your username!',
  'pages.login.password.placeholder': 'Password',
  'pages.login.password.required': 'Please input your password!',
  'pages.login.captcha.placeholder': 'Captcha',
  'pages.login.captcha.required': 'Please input captcha!',
  'pages.login.rememberMe': 'Remember me',
  'pages.login.forgotPassword': 'Forgot Password ?',
  'pages.login.submit': 'Login',
  'pages.login.loginWith': 'Login with :',
  'pages.login.registerAccount': 'Register Account',
  // welcome
  'pages.welcome.link': 'Hi, Welcome!',
  'pages.welcome.alertMessage': 'Login Success',
  'pages.welcome.quickStart': 'Quick Start',
  'pages.welcome.generate': 'Generate Struct',
  'pages.welcome.remove': 'Remove Struct',
  // menu
  'pages.system.menu.add': 'Add Menu',
  'pages.system.menu.edit': 'Edit Menu',
  'pages.system.menu.delTip': 'Are you sure you want to delete this menu and its submenus?',
  'pages.system.menu.button.addChild': 'Add Submenu',
  'pages.system.menu.form.code': 'Code',
  'pages.system.menu.form.code.placeholder': 'Please enter the code',
  'pages.system.menu.form.code.required': 'Code is required!',
  'pages.system.menu.form.name': 'Name',
  'pages.system.menu.form.name.placeholder': 'Please enter the name',
  'pages.system.menu.form.name.required': 'Name is required!',
  'pages.system.menu.form.path': 'Path',
  'pages.system.menu.form.sequence': 'Sequence',
  'pages.system.menu.form.type': 'Type',
  'pages.system.menu.form.type.page': 'Menu',
  'pages.system.menu.form.type.button': 'Button',
  'pages.system.menu.form.status': 'Status',
  'pages.system.menu.form.status.enabled': 'Enabled',
  'pages.system.menu.form.status.disabled': 'Disabled',
  'pages.system.menu.form.description': 'Description',
  'pages.system.menu.form.properties': 'Properties',
  'pages.system.menu.form.parent_name': 'Parent',
  'pages.system.menu.form.created_at': 'Created At',
  'pages.system.menu.form.updated_at': 'Updated At',
  'pages.system.menu.resource.form.basic': 'Basic Information',
  'pages.system.menu.resource.form.title': 'API Resource Configuration',
  'pages.system.menu.resource.form.method': 'Method',
  'pages.system.menu.resource.form.method.placeholder': 'Please select the request method',
  'pages.system.menu.resource.form.path': 'Path',
  'pages.system.menu.resource.form.path.placeholder':
    'Please enter the request path (example: /api/v1/users/:id)',
  // role
  'pages.system.role.add': 'Add Role',
  'pages.system.role.edit': 'Edit Role',
  'pages.system.role.delTip': 'Are you sure you want to delete this role?',
  'pages.system.role.form.code': 'Code',
  'pages.system.role.form.code.placeholder': 'Please enter the code',
  'pages.system.role.form.code.required': 'Code is required!',
  'pages.system.role.form.name': 'Name',
  'pages.system.role.form.name.placeholder': 'Please enter the name',
  'pages.system.role.form.name.required': 'Name is required!',
  'pages.system.role.form.sequence': 'Sequence',
  'pages.system.role.form.status': 'Status',
  'pages.system.role.form.status.enabled': 'Enabled',
  'pages.system.role.form.status.disabled': 'Disabled',
  'pages.system.role.form.description': 'Description',
  'pages.system.role.form.created_at': 'Created At',
  'pages.system.role.form.updated_at': 'Updated At',
  'pages.system.role.form.basic': 'Basic Information',
  'pages.system.role.form.menu': 'Select Menu Permissions',
  'pages.system.role.form.menu.required': 'Please select at least one menu permission',
  // user
  'pages.system.user.add': 'Add User',
  'pages.system.user.edit': 'Edit User',
  'pages.system.user.delTip': 'Are you sure you want to delete this user?',
  'pages.system.user.form.username': 'Username',
  'pages.system.user.form.username.placeholder': 'Please enter the username',
  'pages.system.user.form.username.required': 'Username is required!',
  'pages.system.user.form.password': 'Password',
  'pages.system.user.form.password.placeholder': 'Please input password',
  'pages.system.user.form.password.update.placeholder': 'Do not update password if empty',
  'pages.system.user.form.name': 'Name',
  'pages.system.user.form.name.placeholder': 'Please enter the name',
  'pages.system.user.form.name.required': 'Name is required!',
  'pages.system.user.form.email': 'Email',
  'pages.system.user.form.email.placeholder': 'Please enter the email',
  'pages.system.user.form.phone': 'Phone',
  'pages.system.user.form.phone.placeholder': 'Please enter the phone number',
  'pages.system.user.form.roles': 'Roles',
  'pages.system.user.form.roles.placeholder': 'Please select roles',
  'pages.system.user.form.roles.required': 'Please select at least one role',
  'pages.system.user.form.remark': 'Remark',
  'pages.system.user.form.status': 'Status',
  'pages.system.user.form.status.activated': 'Activated',
  'pages.system.user.form.status.freezed': 'Freezed',
  'pages.system.user.form.created_at': 'Created At',
  'pages.system.user.form.updated_at': 'Updated At',
  // workflow
  'pages.system.workflow.add': 'Add workflow',
  'pages.system.workflow.edit': 'Edit workflow',
  'pages.system.workflow.delTip': 'Are you sure you want to delete this workflow?',
  'pages.system.workflow.form.name': 'Name',
  'pages.system.workflow.form.name.placeholder': 'Please enter the workflow name',
  'pages.system.workflow.form.name.required': 'Workflow name is required!',
  'pages.system.workflow.form.code': 'Type',
  'pages.system.workflow.form.code.placeholder': 'Please select the workflow type',
  'pages.system.workflow.form.code.required': 'Workflow type is required!',
  'pages.system.workflow.form.code.opt.withdraw': 'Withdraw Approval',
  'pages.system.workflow.form.rule': 'Rules',
  'pages.system.workflow.form.rule.placeholder': 'Please enter the workflow rules(json)',
  'pages.system.workflow.form.desc': 'Description',
  'pages.system.workflow.form.desc.placeholder': 'Please enter the workflow description',
  'pages.system.workflow.form.roles': 'Approval Roles',
  'pages.system.workflow.form.roles.placeholder': 'Please select roles',
  'pages.system.workflow.form.roles.required': 'Please select at least one role',
  'pages.system.workflow.form.status': 'Status',
  'pages.system.workflow.form.status.activated': 'Activated',
  'pages.system.workflow.form.status.freezed': 'Freezed',
  'pages.system.workflow.form.created_at': 'Created At',
  'pages.system.workflow.form.updated_at': 'Updated At',
  // approval
  'pages.approval.withdraw.rejectTip': 'Are you sure you want to reject?',

  'pages.approval.withdraw.form.address': 'Address',
  'pages.approval.withdraw.form.chain_name': 'Network',
  'pages.approval.withdraw.form.cc_name': 'Coin Name',
  'pages.approval.withdraw.form.amount': 'Amount',
  'pages.approval.withdraw.form.apply_at': 'Apply Time',
  'pages.approval.withdraw.form.latest_op': 'Latest Update Time',
  'pages.approval.withdraw.form.approval_no': 'Approval No.',
  'pages.approval.withdraw.form.status': 'Status',

  'pages.approval.withdraw.form.status.canceled': 'Candeled',
  'pages.approval.withdraw.form.status.rejected': 'Rejected',
  'pages.approval.withdraw.form.status.approved': 'Approved',
  'pages.approval.withdraw.form.status.pending': 'Pending',
  'pages.approval.withdraw.form.status.onmyturn': 'OnMyTurn',

  'pages.approval.withdraw.form.created_at': 'Created At',
  'pages.approval.withdraw.form.updated_at': 'Updated At',

  // dashboard
  'pages.dashboard.history.days.1': 'Yestoday',
  'pages.dashboard.history.days.7': '+7 Days',
  'pages.dashboard.history.days.14': '+14 Days',
  'pages.dashboard.history.days.30': '+30 Days',
  'pages.dashboard.history.days.all': 'All',

  'pages.dashboard.basic.basic.title': 'Basic Data',
  'pages.dashboard.basic.user.title': 'User Data',
  'pages.dashboard.basic.user.new': 'New User',
  'pages.dashboard.basic.user.act': 'Active User',
  'pages.dashboard.basic.user.game': 'Game User',
  'pages.dashboard.basic.club.title': 'Club Data',
  'pages.dashboard.basic.club.new': 'New Club',
  'pages.dashboard.basic.club.act': 'Active Club',
  'pages.dashboard.basic.club.user': 'Club Users',

  'pages.dashboard.basic.coin.rcwd_title': 'Recharge & Withdraw',
  'pages.dashboard.basic.coin.rcwd_nodata': 'No Data',
  'pages.dashboard.basic.coin.recharge': 'Recharge',
  'pages.dashboard.basic.coin.withdraw': 'Withdraw',

  'pages.dashboard.basic.game.tab.title_all': 'All Games',
  'pages.dashboard.basic.game.tab.title_club': 'Club Games',
  'pages.dashboard.basic.game.tab.title_private': 'Tmp Games',
  'pages.dashboard.basic.game.cnt.title': 'Games Data',
  'pages.dashboard.basic.game.cnt.total': 'Total Games Count',
  'pages.dashboard.basic.game.cnt.avg': 'Avg Hands',
  'pages.dashboard.basic.game.cnt.item': 'Games Count',
  'pages.dashboard.basic.game.fee.tax': 'Tax Fee',
  'pages.dashboard.basic.game.fee.ins': 'PutOption Fee',
  'pages.dashboard.basic.game.fee.dep': 'Depleted Fee',

  // logger
  'pages.system.logger.form.level': 'Level',
  'pages.system.logger.form.message': 'Message',
  'pages.system.logger.form.trace_id': 'Trace ID',
  'pages.system.logger.form.tag': 'Tag',
  'pages.system.logger.form.user_name': 'User Name',
  'pages.system.logger.form.created_at': 'Created At',
  // profile
  'pages.user.profile.tab.basic': 'Basic Information',
  'pages.user.profile.tab.security': 'Security Settings',
  'pages.user.profile.tab.security.form.old_password': 'Old Password',
  'pages.user.profile.tab.security.form.old_password.placeholder': 'Please enter your old password',
  'pages.user.profile.tab.security.form.old_password.required': 'Old password is required!',
  'pages.user.profile.tab.security.form.password': 'New Password',
  'pages.user.profile.tab.security.form.password.placeholder': 'Please enter your new password',
  'pages.user.profile.tab.security.form.password.required': 'New password is required!',
  'pages.user.profile.tab.security.form.confirm_password': 'Confirm Password',
  'pages.user.profile.tab.security.form.confirm_password.placeholder':
    'Please confirm your new password',
  'pages.user.profile.tab.security.form.confirm_password.required': 'Confirm password is required!',
  'pages.user.profile.tab.security.form.confirm_password.validator':
    'The two passwords that you entered do not match!',

  // operation
  'pages.operation.uid': 'Uid',
  'pages.operation.nickname': 'Nick name',
  'pages.operation.address': 'Address',
  'pages.operation.user': 'User',
  'pages.operation.id': 'ID',
  'pages.operation.club.search': 'Club',
};
