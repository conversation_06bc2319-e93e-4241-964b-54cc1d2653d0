export default {
  'pages.layouts.userLayout.title': '欢迎使用',
  'pages.login.accountLogin.tab': '账户密码登录',
  'pages.login.failure': '登录失败，请重试！',
  'pages.login.success': '登录成功！',
  'pages.login.username.placeholder': '用户名',
  'pages.login.username.required': '用户名是必填项！',
  'pages.login.password.placeholder': '密码',
  'pages.login.password.required': '密码是必填项！',
  'pages.login.captcha.placeholder': '验证码',
  'pages.login.captcha.required': '验证码是必填项！',
  'pages.login.rememberMe': '自动登录',
  'pages.login.forgotPassword': '忘记密码 ?',
  'pages.login.submit': '登录',
  'pages.login.loginWith': '其他登录方式 :',
  'pages.login.registerAccount': '注册账户',
  // common (used in multiply pages)
  'pages.common.yes': '是',
  'pages.common.no': '否',
  // welcome
  'pages.welcome.link': '您好，欢迎使用！',
  'pages.welcome.alertMessage': '登录成功',
  'pages.welcome.quickStart': '快速开始',
  'pages.welcome.generate': '生成结构体',
  'pages.welcome.remove': '删除结构体',
  // menu
  'pages.system.menu.add': '添加菜单',
  'pages.system.menu.edit': '编辑菜单',
  'pages.system.menu.delTip': '确定删除该菜单及其下级吗？',
  'pages.system.menu.button.addChild': '添加下级',
  'pages.system.menu.form.code': '编码',
  'pages.system.menu.form.code.placeholder': '请输入编码',
  'pages.system.menu.form.code.required': '编码是必填项！',
  'pages.system.menu.form.name': '名称',
  'pages.system.menu.form.name.placeholder': '请输入名称',
  'pages.system.menu.form.name.required': '名称是必填项！',
  'pages.system.menu.form.path': '访问路径',
  'pages.system.menu.form.sequence': '排序值',
  'pages.system.menu.form.type': '类型',
  'pages.system.menu.form.type.page': '菜单',
  'pages.system.menu.form.type.button': '按钮',
  'pages.system.menu.form.status': '状态',
  'pages.system.menu.form.status.enabled': '启用',
  'pages.system.menu.form.status.disabled': '禁用',
  'pages.system.menu.form.description': '描述',
  'pages.system.menu.form.properties': '属性',
  'pages.system.menu.form.parent_name': '上级菜单',
  'pages.system.menu.form.created_at': '创建时间',
  'pages.system.menu.form.updated_at': '更新时间',
  'pages.system.menu.resource.form.basic': '基本信息',
  'pages.system.menu.resource.form.title': 'API 资源配置',
  'pages.system.menu.resource.form.method': '请求方法',
  'pages.system.menu.resource.form.method.placeholder': '请选择请求方法',
  'pages.system.menu.resource.form.path': '请求路径',
  'pages.system.menu.resource.form.path.placeholder': '请输入请求路径 (示例：/api/v1/users/:id)',
  // role
  'pages.system.role.add': '添加角色',
  'pages.system.role.edit': '编辑角色',
  'pages.system.role.delTip': '确定删除该角色吗？',
  'pages.system.role.form.code': '编码',
  'pages.system.role.form.code.placeholder': '请输入编码',
  'pages.system.role.form.code.required': '编码是必填项！',
  'pages.system.role.form.name': '名称',
  'pages.system.role.form.name.placeholder': '请输入名称',
  'pages.system.role.form.name.required': '名称是必填项！',
  'pages.system.role.form.sequence': '排序值',
  'pages.system.role.form.status': '状态',
  'pages.system.role.form.status.enabled': '启用',
  'pages.system.role.form.status.disabled': '禁用',
  'pages.system.role.form.description': '描述',
  'pages.system.role.form.created_at': '创建时间',
  'pages.system.role.form.updated_at': '更新时间',
  'pages.system.role.form.basic': '基本信息',
  'pages.system.role.form.menu': '选择菜单权限',
  'pages.system.role.form.menu.required': '请至少选择一个菜单权限',
  // user
  'pages.system.user.add': '添加用户',
  'pages.system.user.edit': '编辑用户',
  'pages.system.user.delTip': '确定删除该用户吗？',
  'pages.system.user.form.username': '用户名',
  'pages.system.user.form.username.placeholder': '请输入用户名',
  'pages.system.user.form.username.required': '用户名是必填项！',
  'pages.system.user.form.password': '密码',
  'pages.system.user.form.password.placeholder': '请输入密码',
  'pages.system.user.form.password.update.placeholder': '密码为空则不修改密码',
  'pages.system.user.form.name': '姓名',
  'pages.system.user.form.name.placeholder': '请输入姓名',
  'pages.system.user.form.name.required': '姓名是必填项！',
  'pages.system.user.form.email': '邮箱',
  'pages.system.user.form.email.placeholder': '请输入邮箱',
  'pages.system.user.form.phone': '手机号',
  'pages.system.user.form.phone.placeholder': '请输入手机号',
  'pages.system.user.form.roles': '所属角色',
  'pages.system.user.form.roles.placeholder': '请选择角色',
  'pages.system.user.form.roles.required': '请至少选择一个角色',
  'pages.system.user.form.remark': '备注',
  'pages.system.user.form.status': '状态',
  'pages.system.user.form.status.activated': '正常',
  'pages.system.user.form.status.freezed': '冻结',
  'pages.system.user.form.created_at': '创建时间',
  'pages.system.user.form.updated_at': '更新时间',
  // config
  'pages.system.add': '添加流程',
  'pages.system.edit': '编辑流程',
  'pages.system.delTip': '确定删除该流程吗？',
  'pages.system.form.name': '名称',
  'pages.system.form.name.placeholder': '请输入名称',
  'pages.system.form.name.required': '名称是必填项！',
  'pages.system.form.code': '编码',
  'pages.system.form.code.placeholder': '请输入编码',
  'pages.system.form.code.required': '编码是必填项！',
  'pages.system.form.type': '类型',
  'pages.system.form.type.placeholder': '请选择类型',
  'pages.system.form.type.required': '类型是必选项！',
  'pages.system.form.code.opt.withdraw': '提币审核',
  'pages.system.form.rule': '规则',
  'pages.system.form.rule.placeholder': '请输入规则(json格式)',
  'pages.system.form.desc': '描述',
  'pages.system.form.desc.placeholder': '请输入描述说明',
  'pages.system.form.roles': '审核角色',
  'pages.system.form.roles.placeholder': '请选择角色',
  'pages.system.form.roles.required': '请至少选择一个角色',
  'pages.system.form.status': '状态',
  'pages.system.form.status.activated': '正常',
  'pages.system.form.status.freezed': '冻结',
  'pages.system.form.created_at': '创建时间',
  'pages.system.form.updated_at': '更新时间',
  'pages.system.form.delete.reconfirm': '确定删除吗？',
  // language
  'pages.system.language.add.title': '添加语言',
  'pages.system.language.add.edit': ' 编辑语言',
  'pages.system.language.alias': '别名',
  'pages.system.form.alias.placeholder': '请输入别名',
  'pages.system.form.alias.required': '别名是必填项！',
  // systemTable
  'pages.system.systemTable.upload': '导入',
  'pages.system.systemTable.edit': ' 编辑',
  'pages.system.systemTable.upload.success': '{0} 文件上传成功',
  'pages.system.systemTable.upload.fail': '{0} 文件上传失败',
  // product
  'pages.system.product.add.title': '添加产品',
  'pages.system.product.add.edit': ' 编辑产品',
  'pages.system.product.form.name': '产品名称',
  'pages.system.product.form.name.placeholder': '请输入产品名称',
  'pages.system.product.form.name.select': '请选择产品名称',
  'pages.system.product.form.name.required': '产品名称是必填项！',
  // version
  'pages.system.version.add.title': '添加版本',
  'pages.system.version.add.edit': ' 编辑产品',
  'pages.system.version.status': '状态',
  'pages.system.version.status.release': '发布',
  'pages.system.version.status.save': '保存',
  'pages.system.version.status.release.success': '发布成功',
  'pages.system.version.status.save.success': '保存成功',
  'pages.system.version.status.published': '已发布',
  'pages.system.version.status.pending': '待发布',
  'pages.system.version.release.time': '发布时间',
  'pages.system.version.release.isBigVersion': '是否大版本',
  'pages.system.version.release.yes': '是',
  'pages.system.version.release.no': '否',
  'pages.system.version.release.time.require': '发布时必填发布时间!',
  'pages.system.version.number.only': '版本号只能输入数字及小数点，格式为 x.x.x 或者 x.x',
  'pages.system.version.release.content.require': '发布时需要填写所有语种描述内容',
  'pages.system.version.number': '版本号',
  'pages.system.form.version.placeholder': '请输入版本号',
  'pages.system.form.version.required': '版本号是必填项！',
  // workflow
  'pages.system.workflow.add': '添加流程',
  'pages.system.workflow.edit': '编辑流程',
  'pages.system.workflow.delTip': '确定删除该流程吗？',
  'pages.system.workflow.form.name': '名称',
  'pages.system.workflow.form.name.placeholder': '请输入名称',
  'pages.system.workflow.form.name.required': '名称是必填项！',
  'pages.system.workflow.form.code': '类型',
  'pages.system.workflow.form.code.placeholder': '请选择类型',
  'pages.system.workflow.form.code.required': '类型是必选项！',
  'pages.system.workflow.form.code.opt.withdraw': '提币审核',
  'pages.system.workflow.form.rule': '规则',
  'pages.system.workflow.form.rule.placeholder': '请输入规则(json格式)',
  'pages.system.workflow.form.desc': '描述',
  'pages.system.workflow.form.desc.placeholder': '请输入流程说明',
  'pages.system.workflow.form.roles': '审核角色',
  'pages.system.workflow.form.roles.placeholder': '请选择角色',
  'pages.system.workflow.form.roles.required': '请至少选择一个角色',
  'pages.system.workflow.form.status': '状态',
  'pages.system.workflow.form.status.activated': '正常',
  'pages.system.workflow.form.status.freezed': '冻结',
  'pages.system.workflow.form.created_at': '创建时间',
  'pages.system.workflow.form.updated_at': '更新时间',
  // approval
  'pages.approval.withdraw.rejectTip': '确定拒绝吗？',

  'pages.approval.withdraw.form.address': '地址',
  'pages.approval.withdraw.form.chain_name': '网络',
  'pages.approval.withdraw.form.cc_name': '币种',
  'pages.approval.withdraw.form.amount': '提取数额',
  'pages.approval.withdraw.form.apply_at': '申请时间',
  'pages.approval.withdraw.form.latest_op': '最后更新时间',
  'pages.approval.withdraw.form.approval_no': '审核编号',
  'pages.approval.withdraw.form.status': '状态',

  'pages.approval.withdraw.form.status.canceled': '取消',
  'pages.approval.withdraw.form.status.rejected': '拒绝',
  'pages.approval.withdraw.form.status.approved': '通过',
  'pages.approval.withdraw.form.status.pending': '审核中',
  'pages.approval.withdraw.form.status.onmyturn': '待我审核',

  'pages.approval.withdraw.form.created_at': '创建时间',
  'pages.approval.withdraw.form.updated_at': '更新时间',

  // dashboard
  'pages.dashboard.history.days.1': '昨天',
  'pages.dashboard.history.days.7': '过去 7 天',
  'pages.dashboard.history.days.14': '过去 14 天',
  'pages.dashboard.history.days.30': '过去 30 天',
  'pages.dashboard.history.days.all': '全部',

  'pages.dashboard.basic.basic.title': '基础数据',
  'pages.dashboard.basic.user.title': '用户数据',
  'pages.dashboard.basic.user.online': '在线用户',
  'pages.dashboard.basic.user.new': '新增用户',
  'pages.dashboard.basic.user.playing': '在玩用户',
  'pages.dashboard.basic.user.act': '活跃用户',
  'pages.dashboard.basic.user.game': '游戏用户',
  'pages.dashboard.basic.club.title': '俱乐部数据',
  'pages.dashboard.basic.club.new': '新增俱乐部',
  'pages.dashboard.basic.club.act': '活跃俱乐部',
  'pages.dashboard.basic.club.user': '俱乐部用户数',

  'pages.dashboard.basic.coin.rcwd_title': '充提数据',
  'pages.dashboard.basic.coin.rcwd_nodata': '无数据',
  'pages.dashboard.basic.coin.recharge': '充币',
  'pages.dashboard.basic.coin.withdraw': '提币',

  'pages.dashboard.basic.club.details': 'Club Details',
  'pages.dashboard.basic.game.tab.title_all': '所有游戏',
  'pages.dashboard.basic.game.tab.title_club': '俱乐部',
  'pages.dashboard.basic.game.tab.title_private': '临时游戏',
  'pages.dashboard.basic.game.cnt.title': '游戏数据',
  'pages.dashboard.basic.game.cnt.total': '总游戏数',
  'pages.dashboard.basic.game.cnt.avg': '平均把数',
  'pages.dashboard.basic.game.cnt.item': 'Table',
  'pages.dashboard.basic.game.cnt.hands': 'Hands',
  'pages.dashboard.basic.game.cnt.profit': 'Profit/Hand',
  'pages.dashboard.basic.game.fee.tax': 'Rake',
  'pages.dashboard.basic.game.fee.ins': 'Ins.',
  'pages.dashboard.basic.game.fee.dep': 'Coms.',
  'pages.dashboard.basic.game.fee.tax.club': 'Club',
  'pages.dashboard.basic.game.fee.tax.platform': 'Platform',
  'pages.dashboard.basic.game.fee.tax.private': 'Private',
  'pages.dashboard.basic.game.fee.tax.agent': 'Agent',
  'pages.dashboard.basic.game.club.check': 'Club >',
  // logger
  'pages.system.logger.form.level': '日志级别',
  'pages.system.logger.form.trace_id': '访问 ID',
  'pages.system.logger.form.tag': '标签',
  'pages.system.logger.form.message': '日志内容',
  'pages.system.logger.form.user_name': '用户名称',
  'pages.system.logger.form.created_at': '创建时间',
  // profile
  'pages.user.profile.tab.basic': '基本信息',
  'pages.user.profile.tab.security': '安全设置',
  'pages.user.profile.tab.security.form.old_password': '旧密码',
  'pages.user.profile.tab.security.form.old_password.placeholder': '请输入旧密码',
  'pages.user.profile.tab.security.form.old_password.required': '旧密码是必填项！',
  'pages.user.profile.tab.security.form.password': '新密码',
  'pages.user.profile.tab.security.form.password.placeholder': '请输入新密码',
  'pages.user.profile.tab.security.form.password.required': '新密码是必填项！',
  'pages.user.profile.tab.security.form.confirm_password': '确认密码',
  'pages.user.profile.tab.security.form.confirm_password.placeholder': '请确认新密码',
  'pages.user.profile.tab.security.form.confirm_password.required': '确认密码是必填项！',
  'pages.user.profile.tab.security.form.confirm_password.validator': '两次输入的密码不匹配！',
  // listing
  'pages.game_system.applicant.info': '申请人信息',
  'pages.game_system.project.name': '项目名称',
  'pages.game_system.project.type': '项目类型',
  'pages.game_system.contract.address': '合约地址',
  'pages.game_system.related.url': '相关链接',
  'pages.game_system.applicant.address': '地址',
  'pages.game_system.applicant.email': '邮箱',
  'pages.game_system.applicant.email.search': '申请人邮箱',
  'pages.game_system.social.media': '社交媒体',
  'pages.game_system.project.introduce': '项目介绍',
  'pages.game_system.apply.status': '申请状态',
  'pages.game_system.apply.status.pass': '已上线',
  'pages.game_system.apply.status.pending': '审核中',
  'pages.game_system.offline.confirm': '确定下线吗？',
  'pages.game_system.offline': '下线',
  'pages.game_system.offline.success': '下线成功',
  'pages.game_system.pass': '上线',
  'pages.game_system.pass.confirm': '确认要上线吗？',
  'pages.game_system.ccid.placeholder': '请输入 ccid',
  'pages.game_system.ccid.required': 'ccid 是上线必填项',
  'pages.game_system.copy': '复制',
  'pages.game_system.copy.success': '复制成功',
  'pages.game_system.status.online': '已上线',
  'pages.game_system.status.pending': '审核中',
  // applicant
  'pages.game_system.applicant.name': '姓名',
  'pages.game_system.applicant.phone': '电话',
  'pages.game_system.applicant.city': '城市',
  'pages.game_system.applicant.canWorkLocally': '是否能在岗位所在地工作',
  'pages.game_system.applicant.needWorkPermit': '是否需要工签',
  'pages.game_system.applicant.positionApplied': '申请岗位',
  'pages.game_system.applicant.task': '申请 Task',
  'pages.game_system.applicant.contactInfo': '联系方式',
  'pages.game_system.applicant.selfIntroduction': '自我介绍',
  'pages.game_system.applicant.translate_languages': '能翻译的语种',
  'pages.game_system.hiring.positionName': '职位名称',
  'pages.game_system.hiring.taskName': 'Task 名称',
  'pages.game_system.hiring.payment': '报酬',
  'pages.game_system.hiring.tag': 'Tag',
  'pages.game_system.hiring.category': '分类',
  'pages.game_system.hiring.location': '地点',
  'pages.game_system.hiring.level': '级别',
  'pages.game_system.hiring.workMode': '工作方式',
  'pages.game_system.hiring.aboutJob': 'About the Job',
  'pages.game_system.hiring.whatWeLookForInYou': 'What We Look For In You',
  'pages.game_system.hiring.educationSkills': 'Education & Skills',
  'pages.game_system.hiring.status': '状态',
  'pages.game_system.hiring.operation': '操作',
  'pages.game_system.hiring.add_job': '添加 Job',
  'pages.game_system.hiring.edit_job': '编辑 Job',
  'pages.game_system.hiring.add_task': '添加 Task',
  'pages.game_system.hiring.edit_task': '编辑 Task',
  'pages.game_system.hiring.add': '添加',
  'pages.game_system.hiring.taskType': '任务类型',
  'pages.game_system.hiring.taskType_translate': '翻译',
  'pages.game_system.hiring.taskType_other': '其他',
  'pages.game_system.hiring.release': '发布',
  'pages.game_system.hiring.release_confirm': '确定发布吗？',
  'pages.game_system.hiring.offline_confirm': '确定下线吗？',
  'pages.game_system.hiring.release_success': '上线成功',
  'pages.game_system.hiring.offline_success': '下线成功',
  'pages.game_system.hiring.status_offline': '已下线',
  'pages.monitor.account.coin': '币种',
  'pages.monitor.account.chain': '链',
  'pages.monitor.account.address': '地址',
  'pages.monitor.account.alertThreshold': '告警门槛',
  'pages.monitor.account.save': '保存',
  'pages.monitor.account.search': '查询',
  'pages.monitor.account.reset': '重置',
  'pages.monitor.wallet_balance.chain_search.placeholder': '筛选链',
  'pages.monitor.wallet_balance.all': '余额汇总',
  'pages.monitor.wallet_balance.user_balance': '用户余额',
  'pages.monitor.wallet_balance.center_wallet': '中心化钱包',
  'pages.monitor.wallet_balance.club_balance': 'Club 余额',
  'pages.monitor.wallet_balance.club_normal': 'Club 普通账户',
  'pages.monitor.wallet_balance.club_treasury': ' Club 金库账户',
  'pages.monitor.wallet_balance.club_jackpot': 'Jackpot',
  'pages.monitor.wallet_balance.chain_data': '链上数据',
  'pages.monitor.wallet_balance.chain_total': '已归集总和',
  'pages.monitor.wallet_balance.cold_wallet': '冷钱包',
  'pages.monitor.wallet_balance.hot_wallet': '热钱包',
  'pages.monitor.wallet_balance.no_gather_total': '未归集总和',
  'pages.monitor.wallet_balance.platform_profit': '平台盈利',
  'pages.monitor.wallet_balance.record_profit': 'Rake',
  'pages.monitor.wallet_balance.platform_fee': 'Withdraw Fee',
  'pages.monitor.wallet_balance.game_inside': 'Consumption',
  'pages.monitor.wallet_balance.dividend_transfer': '划转分红',
  'pages.monitor.gather.txn_hash': 'Txn Hash',
  'pages.monitor.gather.block': 'Block',
  'pages.monitor.gather.age': 'Age',
  'pages.monitor.gather.from': 'From',
  'pages.monitor.gather.to': 'To',
  'pages.monitor.gather.amount': 'Amount',
  'pages.monitor.gather.token': 'Token',
  'pages.monitor.gather.result': 'Result',
  'pages.monitor.gather.time': 'time',
  'pages.monitor.gather.already': '已归集',
  'pages.monitor.gather.already_deposit': '已充值',
  'pages.monitor.gather.not_yet': '未归集',
  'pages.timePicker.24h': '24 H',
  'pages.timePicker.7d': '7 D',
  'pages.timePicker.30d': '30 D',
  'pages.timePicker.3m': '3 Month',
  'pages.timePicker.6m': '6 Month',
  'pages.timePicker.1y': '1 Year',
  'pages.timePicker.all': 'All',
  'pages.monitor.gather.no_gather.count': '未归集笔数',
  'pages.monitor.network.uid': 'uid',
  'pages.monitor.network.nickname': '昵称',
  'pages.monitor.network.address': '地址',
  'pages.monitor.network.tg_id': 'TG id',
  'pages.monitor.network.region': '地域',
  'pages.monitor.network.ip': 'ip',
  'pages.monitor.network.terminal': '终端',
  'pages.monitor.network.club_game': 'Club game',
  'pages.monitor.network.game_name': '牌局名称',
  'pages.monitor.network.network_latency': '网络延迟',
  'pages.monitor.network.time': 'time',
  'pages.monitor.network.date': '日期',
  'pages.operation.uid': 'Uid',
  'pages.operation.nickname': 'Nick name',
  'pages.operation.address': 'Address',
  'pages.operation.user': '用户',
  'pages.operation.id': 'ID',
  'pages.operation.txn_hash': 'Txn Hash',
  'pages.operation.coin': '币种',
  'pages.operation.user.tg_id': 'TG ID',
  'pages.operation.user.area': 'Area',
  'pages.operation.user.ip': 'IP',
  'pages.operation.user.channel': 'Channel',
  'pages.operation.user.records': 'Records',
  'pages.operation.user.hands': 'Hands',
  'pages.operation.user.last_active': 'Last Play',
  'pages.operation.user.win_loss': 'W/L now',
  'pages.operation.user.balance': 'Balance',
  'pages.operation.user.deposit': 'Deposit',
  'pages.operation.user.withdraw': 'Withdraw',
  'pages.operation.user.is_freeze_account': '是否封号',
  'pages.operation.user.is_freeze_wallet': '是否冻结资金',
  'pages.operation': '操作',
  'pages.operation.check_history': '操作历史',
  'pages.operation.user.go_record': '牌局记录',
  'pages.operation.user.go_deposit_withdraw': '充提',
  'pages.operation.user.freeze_account': '封号',
  'pages.operation.user.thaw_account': '解封',
  'pages.operation.user.freeze_wallet': '冻结资金',
  'pages.operation.user.thaw_wallet': '解冻资金',
  'pages.operation.user.freeze_account.confirm': '确定封号？',
  'pages.operation.user.thaw_account.confirm': '确定解封？',
  'pages.operation.user.freeze_wallet.confirm': '确定冻结资金？',
  'pages.operation.user.thaw_wallet.confirm': '确定解冻？',
  'pages.operation.club.search': 'Club',
  'pages.operation.club.id': 'Club id',
  'pages.operation.club.name': 'Club name',
  'pages.operation.club.owner': 'Owner',
  'pages.operation.club.owner_uid': 'Owner uid',
  'pages.operation.club.owner_address': 'Owner address',
  'pages.operation.club.region': 'Area',
  'pages.operation.club.earn': 'Earn',
  'pages.operation.club.Vault': 'Vault',
  'pages.operation.club.Jackpot': 'Jackpot',
  'pages.operation.club.income': 'Income',
  'pages.operation.club_income': 'Club income',
  'pages.operation.club.income_rack': 'Rack',
  'pages.operation.club.income_consumption': 'Consumption',
  'pages.operation.club.income_insurance': 'Insurance',
  'pages.operation.club.history_income': 'Total income',
  'pages.operation.club.star_level': 'Star',
  'pages.operation.club.freeze_account': '封 Club',
  'pages.operation.club.freeze_account.confirm': '确定封 Club？',
  'pages.operation.user.set_star': '设置3星',
  'pages.operation.user.club_name_id': 'Club Name ID',
  'pages.operation.user.edit_name_id': '编辑 NameID',
  'pages.operation.success': '操作成功',
  'pages.operation.user.set_star_forever': '设置永久3星',
  'pages.operation.user.set_star_normal': '设置普通3星',
  'pages.operation.user.set_star_cancel': '取消3星',
  'pages.operation.user.set_star.confirm': '确定设置3星？',
  'pages.operation.records.go_income': 'Income',
  'pages.operation.records.record_id': 'Record id',
  'pages.operation.records.record': 'Record',
  'pages.operation.records.name': 'Name',
  'pages.operation.records.club_name': 'Club',
  'pages.operation.records.club': 'Club / Private',
  'pages.operation.token': 'Token',
  'pages.operation.records.blinds': 'Blinds',
  'pages.operation.records.duration': 'Duration',
  'pages.operation.records.insurance': 'Insurance',
  'pages.operation.records.squid': 'Squid',
  'pages.operation.records.ch': 'CH',
  'pages.operation.records.hands': 'Hands',
  'pages.operation.records.user': 'User',
  'pages.operation.records.wl': 'W / L',
  'pages.operation.records.buy_in': 'Buy-in',
  'pages.operation.records.total_spot': 'Total Spot',
  'pages.operation.records.max_spot': 'Max Spot',
  'pages.operation.records.spot_total': 'Total Spot',
  'pages.operation.records.spot_max': 'Max Spot',
  'pages.operation.records.all_in': 'All-in',
  'pages.operation.records.player': 'Player',
  'pages.operation.records.club_star': 'Club star',
  'pages.operation.records.ratio': 'Ratio',
  'pages.operation.records.rake': 'Total Rake',
  'pages.operation.records.cons_total': 'Total Cons.',
  'pages.operation.records.ins_total': 'Total ins.',
  'pages.operation.records.club_income': 'Club Income',
  'pages.operation.records.agent_share': 'Agent share',
  'pages.operation.records.JP': 'JP',
  'pages.operation.records.platform': 'Platform',
  'pages.operation.records.GP': 'GP',
  'pages.operation.records.time': 'time',
  'pages.operation.records.start_time': '开始时间',
  'pages.operation.records.end_time': '结束时间',
  'pages.operation.records.operation': '操作',
  'pages.operation.records.go_bill': '账单',
  'pages.operation.records.go_hands': 'Hands',
  'pages.operation.records.divided_ratio': 'Divided Ratio',
  'pages.operation.records.club_divided': 'Club divided',
  'pages.operation.hands.id': 'Hands id',
  'pages.operation.hands.insurance': 'Insurance',
  'pages.operation.hands.spot': 'Spot',
  'pages.operation.hands.squid': 'Insurance',
  'pages.operation.hands.rake': 'Rake',
  'pages.operation.hands.consumption': 'Consumption',
  'pages.operation.hands.ins': 'Ins.',
  'pages.operation.hands.ins_100': 'ins 100%',
  'pages.operation.hands.belong_record': 'Records',
  'pages.operation.hands.check_action': '查看Action',
  'pages.operation.time': 'Time',
  'pages.operation.type': 'Type',
  'pages.operation.club': 'Club',
  'pages.operation.total': 'Total',
  'pages.operation.agent_share': 'Agent share',
  'pages.operation.deposit_withdraw.deposit': 'Deposit',
  'pages.operation.deposit_withdraw.withdraw': 'Withdraw',
  'pages.operation.transfer_by': 'Transfer by'

};
